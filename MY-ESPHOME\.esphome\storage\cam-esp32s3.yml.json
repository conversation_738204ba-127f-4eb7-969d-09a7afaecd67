{"storage_version": 1, "name": "esp32s3_cam", "friendly_name": null, "comment": null, "esphome_version": "2025.7.2", "src_version": 1, "address": "esp32s3_cam.local", "web_port": 80, "esp_platform": "ESP32S3", "build_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\esp32s3_cam", "firmware_bin_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\esp32s3_cam\\.pioenvs\\esp32s3_cam\\firmware.bin", "loaded_integrations": ["api", "async_tcp", "binary_sensor", "camera", "esp32", "esp32_camera", "esp32_camera_web_server", "esphome", "homeassistant", "json", "logger", "md5", "mdns", "network", "ota", "preferences", "psram", "safe_mode", "socket", "switch", "template", "time", "web_server", "web_server_base", "wifi"], "loaded_platforms": ["binary_sensor/template", "ota/esphome", "switch/template", "time/homeassistant"], "no_mdns": false, "framework": "a<PERSON><PERSON><PERSON>", "core_platform": "esp32"}