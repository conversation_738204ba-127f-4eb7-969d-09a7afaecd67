esphome:
  name: bmp_aht_esp32c3
  friendly_name: 温湿度大气压强传感器节点

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino
# 启用日志
logger:

# 启用 Home Assistant API（可选）
api:

ota:

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  
i2c:
  sda: 4
  scl: 5
  scan: true
  id: bus_a

sensor:
  # AHT10 温湿度传感器（地址固定为 0x38）
  - platform: aht10
    temperature:
      name: "AHT10 温度"
    humidity:
      name: "AHT10 湿度"
    update_interval: 30s

  # BME280 气压传感器（地址通常为 0x76 或 0x77）
  - platform: bmp280
    temperature:
      name: "BME280 温度"
    pressure:
      name: "BME280 气压"
    address: 0x76
    i2c_id: bus_a
    update_interval: 30s
