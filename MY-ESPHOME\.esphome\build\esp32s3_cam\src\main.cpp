// Auto generated code by esphome
// ========== AUTO GENERATED INCLUDE BLOCK BEGIN ===========
#include "esphome.h"
using namespace esphome;
using std::isnan;
using std::min;
using std::max;
using namespace time;
using namespace switch_;
using namespace binary_sensor;
logger::Logger *logger_logger_id;
web_server_base::WebServerBase *web_server_base_webserverbase_id;
wifi::WiFiComponent *wifi_wificomponent_id;
mdns::MDNSComponent *mdns_mdnscomponent_id;
esphome::ESPHomeOTAComponent *esphome_esphomeotacomponent_id;
safe_mode::SafeModeComponent *safe_mode_safemodecomponent_id;
api::APIServer *api_apiserver_id;
using namespace api;
web_server::WebServer *web_server_webserver_id;
const uint8_t ESPHOME_WEBSERVER_INDEX_HTML[174] PROGMEM = {60, 33, 68, 79, 67, 84, 89, 80, 69, 32, 104, 116, 109, 108, 62, 60, 104, 116, 109, 108, 62, 60, 104, 101, 97, 100, 62, 60, 109, 101, 116, 97, 32, 99, 104, 97, 114, 115, 101, 116, 61, 85, 84, 70, 45, 56, 62, 60, 108, 105, 110, 107, 32, 114, 101, 108, 61, 105, 99, 111, 110, 32, 104, 114, 101, 102, 61, 100, 97, 116, 97, 58, 62, 60, 47, 104, 101, 97, 100, 62, 60, 98, 111, 100, 121, 62, 60, 101, 115, 112, 45, 97, 112, 112, 62, 60, 47, 101, 115, 112, 45, 97, 112, 112, 62, 60, 115, 99, 114, 105, 112, 116, 32, 115, 114, 99, 61, 34, 104, 116, 116, 112, 115, 58, 47, 47, 111, 105, 46, 101, 115, 112, 104, 111, 109, 101, 46, 105, 111, 47, 118, 50, 47, 119, 119, 119, 46, 106, 115, 34, 62, 60, 47, 115, 99, 114, 105, 112, 116, 62, 60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62};
const size_t ESPHOME_WEBSERVER_INDEX_HTML_SIZE = 174;
using namespace json;
preferences::IntervalSyncer *preferences_intervalsyncer_id;
homeassistant::HomeassistantTime *homeassistant_time;
time::CronTrigger *time_crontrigger_id;
Automation<> *automation_id;
template_::TemplateSwitch *camera_enable;
Automation<> *automation_id_4;
LambdaAction<> *lambdaaction_id_4;
Automation<> *automation_id_3;
LambdaAction<> *lambdaaction_id_3;
esp32_camera::ESP32Camera *esp32_camera_esp32camera_id;
esp32_camera_web_server::CameraWebServer *esp32_camera_web_server_camerawebserver_id;
esp32_camera_web_server::CameraWebServer *esp32_camera_web_server_camerawebserver_id_2;
template_::TemplateBinarySensor *camera_active;
psram::PsramComponent *psram_psramcomponent_id;
switch_::TurnOffAction<> *switch__turnoffaction_id;
LambdaAction<> *lambdaaction_id;
time::CronTrigger *time_crontrigger_id_2;
Automation<> *automation_id_2;
switch_::TurnOnAction<> *switch__turnonaction_id;
LambdaAction<> *lambdaaction_id_2;
#define yield() esphome::yield()
#define millis() esphome::millis()
#define micros() esphome::micros()
#define delay(x) esphome::delay(x)
#define delayMicroseconds(x) esphome::delayMicroseconds(x)
// ========== AUTO GENERATED INCLUDE BLOCK END ==========="

void setup() {
  // ========== AUTO GENERATED CODE BEGIN ===========
  App.reserve_switch(1);
  App.reserve_binary_sensor(1);
  // network:
  //   enable_ipv6: false
  //   min_ipv6_addr_count: 0
  // async_tcp:
  //   {}
  // esphome:
  //   name: esp32s3_cam
  //   platformio_options:
  //     monitor_speed: '115200'
  //     board_build.arduino.partitions: default_16MB.csv
  //     board_build.arduino.memory_type: qio_opi
  //   min_version: 2025.7.2
  //   build_path: build\esp32s3_cam
  //   friendly_name: ''
  //   includes: []
  //   libraries: []
  //   name_add_mac_suffix: false
  //   debug_scheduler: false
  //   areas: []
  //   devices: []
  App.pre_setup("esp32s3_cam", "", "", __DATE__ ", " __TIME__, false);
  App.reserve_components(18);
  // time:
  // switch:
  // binary_sensor:
  // logger:
  //   id: logger_logger_id
  //   baud_rate: 115200
  //   tx_buffer_size: 512
  //   deassert_rts_dtr: false
  //   task_log_buffer_size: 768
  //   hardware_uart: USB_CDC
  //   level: DEBUG
  //   logs: {}
  logger_logger_id = new logger::Logger(115200, 512);
  logger_logger_id->create_pthread_key();
  logger_logger_id->init_log_buffer(768);
  logger_logger_id->set_log_level(ESPHOME_LOG_LEVEL_DEBUG);
  logger_logger_id->set_uart_selection(logger::UART_SELECTION_USB_CDC);
  logger_logger_id->pre_setup();
  logger_logger_id->set_component_source("logger");
  App.register_component(logger_logger_id);
  // web_server_base:
  //   id: web_server_base_webserverbase_id
  web_server_base_webserverbase_id = new web_server_base::WebServerBase();
  web_server_base_webserverbase_id->set_component_source("web_server_base");
  App.register_component(web_server_base_webserverbase_id);
  web_server_base::global_web_server_base = web_server_base_webserverbase_id;
  // wifi:
  //   id: wifi_wificomponent_id
  //   domain: .local
  //   reboot_timeout: 15min
  //   power_save_mode: LIGHT
  //   fast_connect: false
  //   passive_scan: false
  //   enable_on_boot: true
  //   networks:
  //     - ssid: !secret 'wifi_ssid'
  //       password: !secret 'wifi_password'
  //       id: wifi_wifiap_id
  //       priority: 0.0
  //   use_address: esp32s3_cam.local
  wifi_wificomponent_id = new wifi::WiFiComponent();
  wifi_wificomponent_id->set_use_address("esp32s3_cam.local");
  {
  wifi::WiFiAP wifi_wifiap_id = wifi::WiFiAP();
  wifi_wifiap_id.set_ssid("HOME");
  wifi_wifiap_id.set_password("nb9d30@24zd");
  wifi_wifiap_id.set_priority(0.0f);
  wifi_wificomponent_id->add_sta(wifi_wifiap_id);
  }
  wifi_wificomponent_id->set_reboot_timeout(900000);
  wifi_wificomponent_id->set_power_save_mode(wifi::WIFI_POWER_SAVE_LIGHT);
  wifi_wificomponent_id->set_fast_connect(false);
  wifi_wificomponent_id->set_passive_scan(false);
  wifi_wificomponent_id->set_enable_on_boot(true);
  wifi_wificomponent_id->set_component_source("wifi");
  App.register_component(wifi_wificomponent_id);
  // mdns:
  //   id: mdns_mdnscomponent_id
  //   disabled: false
  //   services: []
  mdns_mdnscomponent_id = new mdns::MDNSComponent();
  mdns_mdnscomponent_id->set_component_source("mdns");
  App.register_component(mdns_mdnscomponent_id);
  // ota:
  // ota.esphome:
  //   platform: esphome
  //   id: esphome_esphomeotacomponent_id
  //   version: 2
  //   port: 3232
  esphome_esphomeotacomponent_id = new esphome::ESPHomeOTAComponent();
  esphome_esphomeotacomponent_id->set_port(3232);
  esphome_esphomeotacomponent_id->set_component_source("esphome.ota");
  App.register_component(esphome_esphomeotacomponent_id);
  // safe_mode:
  //   id: safe_mode_safemodecomponent_id
  //   boot_is_good_after: 1min
  //   disabled: false
  //   num_attempts: 10
  //   reboot_timeout: 5min
  safe_mode_safemodecomponent_id = new safe_mode::SafeModeComponent();
  safe_mode_safemodecomponent_id->set_component_source("safe_mode");
  App.register_component(safe_mode_safemodecomponent_id);
  if (safe_mode_safemodecomponent_id->should_enter_safe_mode(10, 300000, 60000)) return;
  // api:
  //   id: api_apiserver_id
  //   port: 6053
  //   password: ''
  //   reboot_timeout: 15min
  //   batch_delay: 100ms
  //   custom_services: false
  api_apiserver_id = new api::APIServer();
  api_apiserver_id->set_component_source("api");
  App.register_component(api_apiserver_id);
  api_apiserver_id->set_port(6053);
  api_apiserver_id->set_reboot_timeout(900000);
  api_apiserver_id->set_batch_delay(100);
  // web_server:
  //   port: 80
  //   auth:
  //     username: admin
  //     password: !secret 'wifi_password'
  //   id: web_server_webserver_id
  //   version: 2
  //   enable_private_network_access: true
  //   web_server_base_id: web_server_base_webserverbase_id
  //   include_internal: false
  //   log: true
  //   css_url: ''
  //   js_url: https:oi.esphome.io/v2/www.js
  web_server_webserver_id = new web_server::WebServer(web_server_base_webserverbase_id);
  web_server_webserver_id->set_component_source("web_server");
  App.register_component(web_server_webserver_id);
  web_server_base_webserverbase_id->set_port(80);
  web_server_webserver_id->set_expose_log(true);
  web_server_base_webserverbase_id->set_auth_username("admin");
  web_server_base_webserverbase_id->set_auth_password("nb9d30@24zd");
  web_server_webserver_id->set_include_internal(false);
  // json:
  //   {}
  // esp32:
  //   board: esp32-s3-devkitc-1
  //   framework:
  //     version: 3.1.3
  //     advanced:
  //       ignore_efuse_custom_mac: false
  //     source: pioarduino/framework-arduinoespressif32@https:github.com/espressif/arduino-esp32/releases/download/3.1.3/esp32-3.1.3.zip
  //     platform_version: https:github.com/pioarduino/platform-espressif32/releases/download/53.03.13/platform-espressif32.zip
  //     type: arduino
  //   flash_size: 16MB
  //   variant: ESP32S3
  //   cpu_frequency: 160MHZ
  setCpuFrequencyMhz(160);
  // preferences:
  //   id: preferences_intervalsyncer_id
  //   flash_write_interval: 60s
  preferences_intervalsyncer_id = new preferences::IntervalSyncer();
  preferences_intervalsyncer_id->set_write_interval(60000);
  preferences_intervalsyncer_id->set_component_source("preferences");
  App.register_component(preferences_intervalsyncer_id);
  // time.homeassistant:
  //   platform: homeassistant
  //   id: homeassistant_time
  //   on_time:
  //     - seconds:
  //         - 0
  //       minutes:
  //         - 0
  //       hours:
  //         - 0
  //       then:
  //         - switch.turn_off:
  //             id: camera_enable
  //           type_id: switch__turnoffaction_id
  //         - logger.log:
  //             format: Camera automatically disabled at midnight
  //             args: []
  //             level: DEBUG
  //             tag: main
  //           type_id: lambdaaction_id
  //       automation_id: automation_id
  //       trigger_id: time_crontrigger_id
  //     - seconds:
  //         - 0
  //       minutes:
  //         - 0
  //       hours:
  //         - 8
  //       then:
  //         - switch.turn_on:
  //             id: camera_enable
  //           type_id: switch__turnonaction_id
  //         - logger.log:
  //             format: Camera automatically enabled at 8 AM
  //             args: []
  //             level: DEBUG
  //             tag: main
  //           type_id: lambdaaction_id_2
  //       automation_id: automation_id_2
  //       trigger_id: time_crontrigger_id_2
  //   timezone: CST-8
  //   update_interval: 15min
  homeassistant_time = new homeassistant::HomeassistantTime();
  homeassistant_time->set_timezone("CST-8");
  time_crontrigger_id = new time::CronTrigger(homeassistant_time);
  time_crontrigger_id->add_seconds({0});
  time_crontrigger_id->add_minutes({0});
  time_crontrigger_id->add_hours({0});
  time_crontrigger_id->add_days_of_month({1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31});
  time_crontrigger_id->add_months({1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12});
  time_crontrigger_id->add_days_of_week({1, 2, 3, 4, 5, 6, 7});
  time_crontrigger_id->set_component_source("time");
  App.register_component(time_crontrigger_id);
  automation_id = new Automation<>(time_crontrigger_id);
  // switch.template:
  //   platform: template
  //   name: Camera Enable
  //   id: camera_enable
  //   optimistic: true
  //   restore_mode: RESTORE_DEFAULT_ON
  //   turn_on_action:
  //     then:
  //       - logger.log:
  //           format: Camera enabled
  //           args: []
  //           level: DEBUG
  //           tag: main
  //         type_id: lambdaaction_id_3
  //     trigger_id: trigger_id
  //     automation_id: automation_id_3
  //   turn_off_action:
  //     then:
  //       - logger.log:
  //           format: Camera disabled
  //           args: []
  //           level: DEBUG
  //           tag: main
  //         type_id: lambdaaction_id_4
  //     trigger_id: trigger_id_2
  //     automation_id: automation_id_4
  //   disabled_by_default: false
  //   assumed_state: false
  camera_enable = new template_::TemplateSwitch();
  App.register_switch(camera_enable);
  camera_enable->set_name("Camera Enable");
  camera_enable->set_object_id("camera_enable");
  camera_enable->set_disabled_by_default(false);
  camera_enable->set_restore_mode(switch_::SWITCH_RESTORE_DEFAULT_ON);
  camera_enable->set_component_source("template.switch");
  App.register_component(camera_enable);
  automation_id_4 = new Automation<>(camera_enable->get_turn_off_trigger());
  lambdaaction_id_4 = new LambdaAction<>([=]() -> void {
      ESP_LOGD("main", "Camera disabled");
  });
  automation_id_4->add_actions({lambdaaction_id_4});
  automation_id_3 = new Automation<>(camera_enable->get_turn_on_trigger());
  lambdaaction_id_3 = new LambdaAction<>([=]() -> void {
      ESP_LOGD("main", "Camera enabled");
  });
  automation_id_3->add_actions({lambdaaction_id_3});
  camera_enable->set_optimistic(true);
  camera_enable->set_assumed_state(false);
  // esp32_camera:
  //   name: 客厅摄像头
  //   disabled_by_default: false
  //   external_clock:
  //     pin: 15
  //     frequency: 20000000.0
  //   i2c_pins:
  //     sda: 4
  //     scl: 5
  //   data_pins:
  //     - 11
  //     - 9
  //     - 8
  //     - 10
  //     - 12
  //     - 18
  //     - 17
  //     - 16
  //   vsync_pin: 6
  //   href_pin: 7
  //   pixel_clock_pin: 13
  //   resolution: 1600X1200
  //   jpeg_quality: 15
  //   horizontal_mirror: true
  //   brightness: 1
  //   contrast: 1
  //   saturation: 0
  //   aec_mode: AUTO
  //   aec2: true
  //   ae_level: 1
  //   agc_mode: AUTO
  //   agc_gain_ceiling: 16X
  //   wb_mode: AUTO
  //   id: esp32_camera_esp32camera_id
  //   vertical_flip: true
  //   special_effect: NONE
  //   aec_value: 300
  //   agc_value: 0
  //   test_pattern: false
  //   max_framerate: 10.0
  //   idle_framerate: 0.1
  //   frame_buffer_count: 1
  esp32_camera_esp32camera_id = new esp32_camera::ESP32Camera();
  esp32_camera_esp32camera_id->set_name("\345\256\242\345\216\205\346\221\204\345\203\217\345\244\264");
  esp32_camera_esp32camera_id->set_object_id("_____");
  esp32_camera_esp32camera_id->set_disabled_by_default(false);
  esp32_camera_esp32camera_id->set_component_source("esp32_camera");
  App.register_component(esp32_camera_esp32camera_id);
  esp32_camera_esp32camera_id->set_data_pins({11, 9, 8, 10, 12, 18, 17, 16});
  esp32_camera_esp32camera_id->set_vsync_pin(6);
  esp32_camera_esp32camera_id->set_href_pin(7);
  esp32_camera_esp32camera_id->set_pixel_clock_pin(13);
  esp32_camera_esp32camera_id->set_jpeg_quality(15);
  esp32_camera_esp32camera_id->set_vertical_flip(true);
  esp32_camera_esp32camera_id->set_horizontal_mirror(true);
  esp32_camera_esp32camera_id->set_contrast(1);
  esp32_camera_esp32camera_id->set_brightness(1);
  esp32_camera_esp32camera_id->set_saturation(0);
  esp32_camera_esp32camera_id->set_special_effect(esp32_camera::ESP32_SPECIAL_EFFECT_NONE);
  esp32_camera_esp32camera_id->set_aec_mode(esp32_camera::ESP32_GC_MODE_AUTO);
  esp32_camera_esp32camera_id->set_aec2(true);
  esp32_camera_esp32camera_id->set_ae_level(1);
  esp32_camera_esp32camera_id->set_aec_value(300);
  esp32_camera_esp32camera_id->set_agc_mode(esp32_camera::ESP32_GC_MODE_AUTO);
  esp32_camera_esp32camera_id->set_agc_value(0);
  esp32_camera_esp32camera_id->set_agc_gain_ceiling(esp32_camera::ESP32_GAINCEILING_16X);
  esp32_camera_esp32camera_id->set_wb_mode(esp32_camera::ESP32_WB_MODE_AUTO);
  esp32_camera_esp32camera_id->set_test_pattern(false);
  esp32_camera_esp32camera_id->set_external_clock(15, 20000000.0f);
  esp32_camera_esp32camera_id->set_i2c_pins(4, 5);
  esp32_camera_esp32camera_id->set_max_update_interval(100.0f);
  esp32_camera_esp32camera_id->set_idle_update_interval(10000.0f);
  esp32_camera_esp32camera_id->set_frame_buffer_count(1);
  esp32_camera_esp32camera_id->set_frame_size(esp32_camera::ESP32_CAMERA_SIZE_1600X1200);
  // esp32_camera_web_server:
  //   port: 8080
  //   mode: STREAM
  //   id: esp32_camera_web_server_camerawebserver_id
  esp32_camera_web_server_camerawebserver_id = new esp32_camera_web_server::CameraWebServer();
  esp32_camera_web_server_camerawebserver_id->set_port(8080);
  esp32_camera_web_server_camerawebserver_id->set_mode(esp32_camera_web_server::STREAM);
  esp32_camera_web_server_camerawebserver_id->set_component_source("esp32_camera_web_server");
  App.register_component(esp32_camera_web_server_camerawebserver_id);
  // esp32_camera_web_server:
  //   port: 8081
  //   mode: SNAPSHOT
  //   id: esp32_camera_web_server_camerawebserver_id_2
  esp32_camera_web_server_camerawebserver_id_2 = new esp32_camera_web_server::CameraWebServer();
  esp32_camera_web_server_camerawebserver_id_2->set_port(8081);
  esp32_camera_web_server_camerawebserver_id_2->set_mode(esp32_camera_web_server::SNAPSHOT);
  esp32_camera_web_server_camerawebserver_id_2->set_component_source("esp32_camera_web_server");
  App.register_component(esp32_camera_web_server_camerawebserver_id_2);
  // binary_sensor.template:
  //   platform: template
  //   name: Camera Active Status
  //   id: camera_active
  //   lambda: !lambda |-
  //      检查当前时间是否在关闭时间段内（00:00-08:00）
  //     auto time = id(homeassistant_time).now();
  //     if (!time.is_valid()) return true;  如果时间无效，默认开启
  //   
  //     int hour = time.hour;
  //     bool in_sleep_time = (hour >= 0 && hour < 8);
  //   
  //      如果在睡眠时间内，返回false（摄像头关闭）
  //      如果不在睡眠时间内，返回开关状态
  //     if (in_sleep_time) {
  //       return false;
  //     } else {
  //       return id(camera_enable).state;
  //     }
  //   disabled_by_default: false
  camera_active = new template_::TemplateBinarySensor();
  App.register_binary_sensor(camera_active);
  camera_active->set_name("Camera Active Status");
  camera_active->set_object_id("camera_active_status");
  camera_active->set_disabled_by_default(false);
  camera_active->set_trigger_on_initial_state(false);
  camera_active->set_component_source("template.binary_sensor");
  App.register_component(camera_active);
  camera_active->set_template([=]() -> esphome::optional<bool> {
      #line 105 "cam-esp32s3.yml"
       
      auto time = homeassistant_time->now();
      if (!time.is_valid()) return true;  
      
      int hour = time.hour;
      bool in_sleep_time = (hour >= 0 && hour < 8);
      
       
       
      if (in_sleep_time) {
        return false;
      } else {
        return camera_enable->state;
      }
  });
  // socket:
  //   implementation: bsd_sockets
  // md5:
  // psram:
  //   id: psram_psramcomponent_id
  //   mode: quad
  //   enable_ecc: false
  //   speed: 40MHZ
  psram_psramcomponent_id = new psram::PsramComponent();
  psram_psramcomponent_id->set_component_source("psram");
  App.register_component(psram_psramcomponent_id);
  switch__turnoffaction_id = new switch_::TurnOffAction<>(camera_enable);
  lambdaaction_id = new LambdaAction<>([=]() -> void {
      ESP_LOGD("main", "Camera automatically disabled at midnight");
  });
  automation_id->add_actions({switch__turnoffaction_id, lambdaaction_id});
  time_crontrigger_id_2 = new time::CronTrigger(homeassistant_time);
  time_crontrigger_id_2->add_seconds({0});
  time_crontrigger_id_2->add_minutes({0});
  time_crontrigger_id_2->add_hours({8});
  time_crontrigger_id_2->add_days_of_month({1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31});
  time_crontrigger_id_2->add_months({1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12});
  time_crontrigger_id_2->add_days_of_week({1, 2, 3, 4, 5, 6, 7});
  time_crontrigger_id_2->set_component_source("time");
  App.register_component(time_crontrigger_id_2);
  automation_id_2 = new Automation<>(time_crontrigger_id_2);
  switch__turnonaction_id = new switch_::TurnOnAction<>(camera_enable);
  lambdaaction_id_2 = new LambdaAction<>([=]() -> void {
      ESP_LOGD("main", "Camera automatically enabled at 8 AM");
  });
  automation_id_2->add_actions({switch__turnonaction_id, lambdaaction_id_2});
  homeassistant_time->set_update_interval(900000);
  homeassistant_time->set_component_source("homeassistant.time");
  App.register_component(homeassistant_time);
  // =========== AUTO GENERATED CODE END ============
  App.setup();
}

void loop() {
  App.loop();
}
