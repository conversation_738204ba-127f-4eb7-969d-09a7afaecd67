esphome:
  name: esp32s3_cam
  platformio_options:
    monitor_speed: 115200
    board_build.arduino.partitions: default_16MB.csv
    board_build.arduino.memory_type: qio_opi

esp32:
  board: esp32-s3-devkitc-1
  framework:
    type: arduino
  flash_size: 16MB

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

# 可选：开启日志与 OTA
logger:

# Home Assistant API - 必需用于集成
api:

# OTA 更新
ota:
  platform: esphome

# 时间同步 - 用于定时控制
time:
  - platform: homeassistant
    id: homeassistant_time

# 摄像头开关控制
switch:
  - platform: template
    name: "Camera Enable"
    id: camera_enable
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - logger.log: "Camera enabled"
    turn_off_action:
      - logger.log: "Camera disabled"

# 自动化 - 定时控制摄像头
automation:
  # 晚上2点关闭摄像头
  - trigger:
      - platform: time
        at: "00:00:00"
    action:
      - switch.turn_off: camera_enable
      - logger.log: "Camera automatically disabled at midnight"

  # 早上8点开启摄像头
  - trigger:
      - platform: time
        at: "08:00:00"
    action:
      - switch.turn_on: camera_enable
      - logger.log: "Camera automatically enabled at 8 AM"

# 摄像头配置（ESP32S3_EYE 模块配置）
esp32_camera:
  name: "客厅摄像头"
  # 根据开关状态控制摄像头
  disabled_by_default: false
  external_clock:
    pin: GPIO15           # XCLK_GPIO_NUM
    frequency: 20MHz
  i2c_pins:
    sda: GPIO4            # SIOD_GPIO_NUM
    scl: GPIO5            # SIOC_GPIO_NUM
  data_pins: [GPIO11, GPIO9, GPIO8, GPIO10, GPIO12, GPIO18, GPIO17, GPIO16]  # Y2-Y9
  vsync_pin: GPIO6        # VSYNC_GPIO_NUM
  href_pin: GPIO7         # HREF_GPIO_NUM
  pixel_clock_pin: GPIO13 # PCLK_GPIO_NUM
  # power_down_pin: -1    # PWDN_GPIO_NUM (未使用)
  # reset_pin: -1         # RESET_GPIO_NUM (未使用)
  resolution: 1600x1200  # UXGA - 最高分辨率
  jpeg_quality: 15        # 适合高分辨率的质量设置 (10=最高质量, 63=最低质量)
  horizontal_mirror: true # 左右翻转图像(无效)
  # 图像调整参数 - 改善亮度
  brightness: 1           # 亮度调整 (-2 到 2, 默认0)
  contrast: 1             # 对比度调整 (-2 到 2, 默认0)
  saturation: 0           # 饱和度调整 (-2 到 2, 默认0)
  # 自动曝光和增益控制
  aec_mode: auto          # 自动曝光控制
  aec2: true              # 启用高级自动曝光
  ae_level: 1             # 自动曝光级别 (-2 到 2, 默认0)
  agc_mode: auto          # 自动增益控制
  agc_gain_ceiling: 16x   # 增益上限 (2x, 4x, 8x, 16x, 32x, 64x, 128x)
  wb_mode: auto           # 自动白平衡

# HTTP 流媒体 - 根据开关状态控制
esp32_camera_web_server:
  - port: 8080
    mode: stream
  - port: 8081
    mode: snapshot

# 二进制传感器 - 显示摄像头状态
binary_sensor:
  - platform: template
    name: "Camera Active Status"
    id: camera_active
    lambda: |-
      // 检查当前时间是否在关闭时间段内（00:00-08:00）
      auto time = id(homeassistant_time).now();
      if (!time.is_valid()) return true; // 如果时间无效，默认开启

      int hour = time.hour;
      bool in_sleep_time = (hour >= 0 && hour < 8);

      // 如果在睡眠时间内，返回false（摄像头关闭）
      // 如果不在睡眠时间内，返回开关状态
      if (in_sleep_time) {
        return false;
      } else {
        return id(camera_enable).state;
      }
    update_interval: 60s


# Web服务器
web_server:
  port: 80
  auth:
    username: admin
    password: !secret wifi_password
