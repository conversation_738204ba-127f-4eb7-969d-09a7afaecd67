esphome:
  name: esp32s3_cam
  platformio_options:
    monitor_speed: 115200
    board_build.arduino.partitions: default_16MB.csv
    board_build.arduino.memory_type: qio_opi
    
on_boot:
  then:
    - delay: 45s  
    - http_request.post:
        url: http://homeassistant.local:8123/api/webhook/-fe2L5ywUXKSCnOLF03R1_V3Z
        headers:
          Authorization: !secret hass_token
esp32:
  board: esp32-s3-devkitc-1
  framework:
    type: arduino
  flash_size: 16MB

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

# Home Assistant API - 必需用于集成
api:

# OTA 更新
ota:
  platform: esphome

# 同步 Home Assistant 时间
time:
  - platform: homeassistant
    id: ha_time
    on_time:
      # 每天 00:00 进入夜间长睡眠
      - seconds: 0
        minutes: 0
        hours: 0
        then:
          - logger.log: "进入夜间深度睡眠"
          - deep_sleep.enter:
              id: deep_sleep_control
              sleep_duration: 8h

# 深度睡眠配置：默认每次运行 1 分钟，睡眠 10 分钟
deep_sleep:
  id: deep_sleep_control
  run_duration: 60s        # 每次运行 60 秒
  sleep_duration: 10min    # 然后睡 10 分钟

# 启动时日志
logger:
  level: INFO
  logs:
    deep_sleep: INFO

# 摄像头配置（ESP32S3_EYE 模块配置）
esp32_camera:
  name: "客厅摄像头"
  # 根据开关状态控制摄像头
  disabled_by_default: false
  external_clock:
    pin: GPIO15           # XCLK_GPIO_NUM
    frequency: 20MHz
  i2c_pins:
    sda: GPIO4            # SIOD_GPIO_NUM
    scl: GPIO5            # SIOC_GPIO_NUM
  data_pins: [GPIO11, GPIO9, GPIO8, GPIO10, GPIO12, GPIO18, GPIO17, GPIO16]  # Y2-Y9
  vsync_pin: GPIO6        # VSYNC_GPIO_NUM
  href_pin: GPIO7         # HREF_GPIO_NUM
  pixel_clock_pin: GPIO13 # PCLK_GPIO_NUM
  # power_down_pin: -1    # PWDN_GPIO_NUM (未使用，因为整个设备会睡眠)
  # reset_pin: -1         # RESET_GPIO_NUM (未使用)
  resolution: 1600x1200  # UXGA - 最高分辨率
  jpeg_quality: 15        # 适合高分辨率的质量设置 (10=最高质量, 63=最低质量)
  horizontal_mirror: true # 左右翻转图像(无效)
  # 图像调整参数 - 改善亮度
  brightness: 1           # 亮度调整 (-2 到 2, 默认0)
  contrast: 1             # 对比度调整 (-2 到 2, 默认0)
  saturation: 0           # 饱和度调整 (-2 到 2, 默认0)
  # 自动曝光和增益控制
  aec_mode: auto          # 自动曝光控制
  aec2: true              # 启用高级自动曝光
  ae_level: 1             # 自动曝光级别 (-2 到 2, 默认0)
  agc_mode: auto          # 自动增益控制
  agc_gain_ceiling: 16x   # 增益上限 (2x, 4x, 8x, 16x, 32x, 64x, 128x)
  wb_mode: auto           # 自动白平衡

# HTTP 流媒体 - 根据开关状态控制
esp32_camera_web_server:
  - port: 8080
    mode: stream
  - port: 8081
    mode: snapshot

# 设备状态传感器
text_sensor:
  - platform: template
    name: "Device Status"
    id: device_status
    update_interval: 30s
    lambda: |-
      auto time = id(homeassistant_time).now();
      if (!time.is_valid()) return {"Time not synced"};

      int hour = time.hour;
      if (hour >= 0 && hour < 8) {
        return {"Should be sleeping"};
      } else {
        return {"Active"};
      }


# Web服务器
web_server:
  port: 80
  auth:
    username: admin
    password: !secret wifi_password


